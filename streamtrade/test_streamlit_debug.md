# Testing Debug Logging dengan Streamlit UI

**Date**: 2025-06-28  
**Status**: Ready for Testing  
**Purpose**: Validasi debug logging dalam environment Streamlit UI  

## 1. Persiapan Testing

### 1.1 Aktivasi Environment
```bash
# Aktivasi virtual environment
source /home/<USER>/Learn/python/notebooks/venv/bin/activate

# Navigate ke streamtrade directory
cd /home/<USER>/Learn/python/notebooks/streamtrade
```

### 1.2 Jalankan Streamlit Server
```bash
# Jalankan Streamlit app
streamlit run run_streamlit.py
```

## 2. Testing Scenarios

### 2.1 Initial Data Loading Test

**Steps**:
1. Buka Streamlit app di browser
2. Di sidebar, pilih:
   - Currency Pair: EURUSD
   - Timeframe: H1
   - Data Loading: N Days Back
   - Days Back: 5
3. Klik "Load Data"
4. **Monitor terminal** untuk debug output

**Expected Debug Output**:
```
============================================================
🔄 INITIAL DATA LOADING
============================================================
📊 Pair: EURUSD
⏰ Timeframe: H1
📅 Days Back: 5
📅 Date Range: 2024-12-26 to 2024-12-31

📁 Data Source: FRESH LOAD FROM FILES
📊 M1 Candles Loaded: ~7200
📊 Converted Candles: ~120
📺 Displayed Candles: ~120
============================================================
```

### 2.2 Sidebar Timeframe Switching Test

**Steps**:
1. Setelah data loaded, di sidebar bagian "Quick Timeframe Switch"
2. Klik button "H4"
3. **Monitor terminal** untuk debug output
4. Klik button "M15"
5. **Monitor terminal** lagi

**Expected Debug Output untuk H4**:
```
============================================================
🔄 TIMEFRAME SWITCHING
============================================================
📊 Pair: EURUSD
⏰ New Timeframe: H4
📅 Using Cached Date Range: 2024-12-26 to 2024-12-31

💾 Data Source: DISK CACHE (atau 📁 FRESH LOAD)
📊 Converted Candles: ~30
📺 Displayed Candles: ~30
============================================================
```

### 2.3 Chart Timeframe Switching Test

**Steps**:
1. Di area chart, lihat timeframe selector di atas chart
2. Pilih timeframe berbeda (misal D1)
3. **Monitor terminal** untuk debug output
4. Switch ke timeframe lain (misal M5)
5. **Monitor terminal** lagi

**Expected Debug Output**:
```
============================================================
🔄 TIMEFRAME SWITCHING
============================================================
📊 Pair: EURUSD
⏰ New Timeframe: D1
📅 Using Cached Date Range: 2024-12-26 to 2024-12-31

📁 Data Source: FRESH LOAD FROM FILES
📊 M1 Candles Loaded: ~7200
📊 Converted Candles: ~5
📺 Displayed Candles: ~5
============================================================
```

### 2.4 Different Pair Loading Test

**Steps**:
1. Di sidebar, ganti Currency Pair ke GBPUSD
2. Ganti timeframe ke H4
3. Ganti Days Back ke 3
4. Klik "Load Data"
5. **Monitor terminal** untuk debug output

**Expected Debug Output**:
```
============================================================
🔄 INITIAL DATA LOADING
============================================================
📊 Pair: GBPUSD
⏰ Timeframe: H4
📅 Days Back: 3
📅 Date Range: 2024-12-28 to 2024-12-31

📁 Data Source: FRESH LOAD FROM FILES
📊 M1 Candles Loaded: ~4320
📊 Converted Candles: ~18
📺 Displayed Candles: ~18
============================================================
```

### 2.5 Cache Hit Testing

**Steps**:
1. Setelah load GBPUSD H4, switch ke H1
2. Kemudian switch kembali ke H4
3. **Monitor terminal** - seharusnya ada cache hit

**Expected Debug Output**:
```
============================================================
🔄 TIMEFRAME SWITCHING
============================================================
📊 Pair: GBPUSD
⏰ New Timeframe: H4
📅 Using Cached Date Range: 2024-12-28 to 2024-12-31

💾 Data Source: DISK CACHE
📊 Converted Candles: ~18
📺 Displayed Candles: ~18
============================================================
```

## 3. Validation Checklist

### 3.1 Debug Output Validation
- [ ] Debug output muncul di terminal saat load data
- [ ] Debug output muncul saat switch timeframe di sidebar
- [ ] Debug output muncul saat switch timeframe di chart
- [ ] Format debug output sesuai dengan expected format
- [ ] Informasi pair, timeframe, dan date range benar
- [ ] Data source (cache vs fresh) teridentifikasi dengan benar

### 3.2 Data Accuracy Validation
- [ ] M1 candles loaded count masuk akal untuk days back
- [ ] Converted candles count sesuai dengan timeframe ratio
- [ ] Displayed candles count tidak melebihi max display limit
- [ ] Cache hit terjadi untuk timeframe yang sudah pernah di-load
- [ ] Fresh load terjadi untuk timeframe baru atau pair baru

### 3.3 Performance Validation
- [ ] Loading time < 5 detik untuk initial load
- [ ] Timeframe switching < 2 detik
- [ ] Cache hit hampir instant (< 0.5 detik)
- [ ] UI tetap responsive selama loading
- [ ] Tidak ada error atau exception di terminal

### 3.4 Cache Behavior Validation
- [ ] Disk cache hit untuk timeframe yang sudah di-convert
- [ ] Memory cache hit untuk data yang baru saja di-access
- [ ] Fresh load untuk timeframe baru atau pair baru
- [ ] M1 base data di-reuse untuk multiple timeframe conversion
- [ ] Independent caching per currency pair

## 4. Troubleshooting

### 4.1 Tidak Ada Debug Output
**Possible Causes**:
- Terminal tidak menampilkan output dari Streamlit
- Debug logging tidak aktif
- Import error atau exception

**Solutions**:
- Pastikan menjalankan Streamlit dari terminal yang sama
- Check log files di `logs/` directory
- Restart Streamlit server

### 4.2 Debug Output Format Salah
**Possible Causes**:
- Code modification error
- Missing print statements
- Exception dalam debug code

**Solutions**:
- Check `enhanced_data_manager.py` untuk debug print statements
- Verify tidak ada syntax error
- Check exception handling

### 4.3 Cache Tidak Bekerja
**Possible Causes**:
- Disk cache directory permission
- Cache corruption
- Settings configuration error

**Solutions**:
- Check `streamtrade/cache/` directory exists dan writable
- Clear cache directory dan restart
- Verify user settings configuration

## 5. Expected Results Summary

Setelah testing lengkap, Anda harus melihat:

1. **Debug output yang konsisten** untuk semua operasi loading dan switching
2. **Cache hit behavior** yang benar untuk timeframe yang sudah di-load
3. **Fresh load behavior** untuk timeframe atau pair baru
4. **Data accuracy** dengan conversion ratios yang benar
5. **Performance yang baik** dengan loading time yang cepat

Jika semua validation checklist ✅, maka debug logging system siap untuk production dan platform ready untuk Phase 5.5 development.

## 6. Next Steps

Setelah Streamlit UI testing selesai:
1. Update dokumentasi dengan hasil testing
2. Mark Phase 5.4+ sebagai complete
3. Proceed ke Phase 5.5 - Advanced Indicator Cache Strategy
