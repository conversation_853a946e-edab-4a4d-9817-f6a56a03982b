#!/usr/bin/env python3
"""
Script untuk menjalankan debug test dengan environment yang benar.
"""

import subprocess
import sys
from pathlib import Path

def run_debug_test():
    """Run debug test dengan virtual environment."""
    
    # Path ke virtual environment
    venv_path = "/home/<USER>/Learn/python/notebooks/venv/bin/activate"
    
    # Path ke test script
    test_script = Path(__file__).parent / "test_debug_logging.py"
    
    # Command untuk menjalankan test
    cmd = f"source {venv_path} && cd {Path(__file__).parent} && python {test_script}"
    
    print("🚀 Running Debug Logging Test...")
    print(f"📁 Working Directory: {Path(__file__).parent}")
    print(f"🐍 Virtual Environment: {venv_path}")
    print(f"📄 Test Script: {test_script}")
    print("="*80)
    
    try:
        # Run the command
        result = subprocess.run(
            cmd,
            shell=True,
            executable="/bin/bash",
            capture_output=False,
            text=True,
            cwd=Path(__file__).parent
        )
        
        if result.returncode == 0:
            print("\n✅ Debug test completed successfully!")
        else:
            print(f"\n❌ Debug test failed with return code: {result.returncode}")
            
    except Exception as e:
        print(f"❌ Error running debug test: {e}")

if __name__ == "__main__":
    run_debug_test()
