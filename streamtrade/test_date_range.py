#!/usr/bin/env python3
"""
Test script untuk memeriksa available date range.
"""

import sys
from pathlib import Path

# Add parent directory to path for proper imports
sys.path.append(str(Path(__file__).parent.parent))

from streamtrade.data.data_loader import DataLoader
from streamtrade.config.logging_config import setup_logging

def test_date_range():
    """Test available date range untuk beberapa pairs."""
    
    # Setup logging
    setup_logging(level="INFO", console_output=True)
    
    # Initialize data loader
    loader = DataLoader()
    
    pairs = ['GBPUSD', 'EURUSD', 'XAUUSD']
    
    for pair in pairs:
        print(f"\n📊 Testing {pair}:")
        try:
            date_range = loader.get_available_date_range(pair)
            if date_range:
                start_date, end_date = date_range
                print(f"  ✅ Start: {start_date}")
                print(f"  ✅ End: {end_date}")
                print(f"  📅 Duration: {(end_date - start_date).days} days")
            else:
                print(f"  ❌ No date range found")
        except Exception as e:
            print(f"  ❌ Error: {e}")

if __name__ == "__main__":
    test_date_range()
