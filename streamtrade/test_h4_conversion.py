#!/usr/bin/env python3
"""
Test script untuk H4 conversion setelah perbaikan.
"""

import sys
from pathlib import Path
import pandas as pd

# Add parent directory to path for proper imports
sys.path.append(str(Path(__file__).parent.parent))

from streamtrade.data.enhanced_data_manager import EnhancedDataManager
from streamtrade.config.logging_config import setup_logging

def test_h4_conversion():
    """Test H4 conversion dengan data GBPUSD."""
    
    # Setup logging
    setup_logging(level="INFO", console_output=True)
    
    # Initialize Enhanced Data Manager
    data_manager = EnhancedDataManager()
    
    pair = "GBPUSD"
    days_back = 5
    
    print(f"🔍 TESTING H4 CONVERSION AFTER FIX")
    print(f"{'='*60}")
    print(f"📊 Pair: {pair}")
    print(f"📅 Days Back: {days_back}")
    
    # Test 1: Load M15 data first
    print(f"\n1️⃣ Loading M15 data...")
    m15_data = data_manager.load_n_days_back(pair, "M15", days_back)
    
    if m15_data is None or m15_data.empty:
        print("❌ Failed to load M15 data")
        return
    
    print(f"✅ M15 data loaded: {len(m15_data)} candles")
    print(f"📅 M15 range: {m15_data.index[0]} to {m15_data.index[-1]}")
    
    # Test 2: Load H4 data (this should work now)
    print(f"\n2️⃣ Loading H4 data...")
    h4_data = data_manager.load_n_days_back(pair, "H4", days_back)
    
    if h4_data is None or h4_data.empty:
        print("❌ Failed to load H4 data - STILL BROKEN!")
        return
    
    print(f"✅ H4 data loaded: {len(h4_data)} candles")
    print(f"📅 H4 range: {h4_data.index[0]} to {h4_data.index[-1]}")
    
    # Test 3: Analyze H4 data quality
    print(f"\n3️⃣ Analyzing H4 data quality...")
    
    # Check timestamps
    print(f"📊 First 5 H4 timestamps:")
    for i, timestamp in enumerate(h4_data.index[:5]):
        candle = h4_data.iloc[i]
        print(f"   {i+1}. {timestamp} - O:{candle['open']:.5f} H:{candle['high']:.5f} L:{candle['low']:.5f} C:{candle['close']:.5f}")
    
    # Check for 4-hour intervals
    if len(h4_data) > 1:
        time_diff = h4_data.index[1] - h4_data.index[0]
        expected_diff = pd.Timedelta(hours=4)
        print(f"📊 Time interval: {time_diff} (expected: {expected_diff})")
        
        if time_diff == expected_diff:
            print("✅ Correct 4-hour intervals")
        else:
            print("⚠️  WARNING: Incorrect time intervals")
    
    # Test 4: Compare with reference data
    print(f"\n4️⃣ Comparing with reference data...")
    
    # Load reference H4 data
    ref_file = Path(__file__).parent / 'cache' / 'tmp' / 'FX_GBPUSD, 240.csv'
    if ref_file.exists():
        ref_data = pd.read_csv(ref_file)
        ref_data['time'] = pd.to_datetime(ref_data['time'])
        ref_data.set_index('time', inplace=True)
        
        print(f"📊 Reference H4 data: {len(ref_data)} candles")
        print(f"📅 Reference range: {ref_data.index[0]} to {ref_data.index[-1]}")
        
        # Find overlapping time range
        our_start = h4_data.index[0]
        our_end = h4_data.index[-1]
        ref_overlap = ref_data[(ref_data.index >= our_start) & (ref_data.index <= our_end)]
        
        if not ref_overlap.empty:
            print(f"📊 Overlapping period: {len(ref_overlap)} reference candles")
            
            # Compare first few candles
            print(f"\n📊 Comparison (first 3 overlapping candles):")
            for i in range(min(3, len(ref_overlap), len(h4_data))):
                our_candle = h4_data.iloc[i]
                ref_time = ref_overlap.index[i]
                ref_candle = ref_overlap.iloc[i]
                
                print(f"   {i+1}. Our:  {h4_data.index[i]} - O:{our_candle['open']:.5f} H:{our_candle['high']:.5f} L:{our_candle['low']:.5f} C:{our_candle['close']:.5f}")
                print(f"      Ref:  {ref_time} - O:{ref_candle['open']:.5f} H:{ref_candle['high']:.5f} L:{ref_candle['low']:.5f} C:{ref_candle['close']:.5f}")
                
                # Check if prices are similar (within 0.001)
                price_diff = abs(our_candle['close'] - ref_candle['close'])
                if price_diff < 0.001:
                    print(f"      ✅ Prices match (diff: {price_diff:.6f})")
                else:
                    print(f"      ⚠️  Price difference: {price_diff:.6f}")
        else:
            print("❌ No overlapping data found")
    else:
        print("❌ Reference file not found")
    
    # Test 5: Test timeframe switching
    print(f"\n5️⃣ Testing timeframe switching...")
    
    # Switch from M15 to H4
    print(f"Switching from M15 to H4...")
    h4_switched = data_manager.switch_timeframe(pair, "H4")
    
    if h4_switched is None or h4_switched.empty:
        print("❌ Timeframe switching failed")
    else:
        print(f"✅ Timeframe switching successful: {len(h4_switched)} candles")
        
        # Compare with direct load
        if len(h4_switched) == len(h4_data):
            print("✅ Switched data matches direct load")
        else:
            print(f"⚠️  Data length mismatch: switched={len(h4_switched)}, direct={len(h4_data)}")
    
    print(f"\n{'='*60}")
    print("🎯 H4 Conversion Test Completed!")

if __name__ == "__main__":
    test_h4_conversion()
