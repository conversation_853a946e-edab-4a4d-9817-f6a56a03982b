#!/usr/bin/env python3
"""
Test script untuk debug logging sistem loading dan cache data.
Menguji loading pertama kali dan switching timeframe dengan informasi detail.
"""

import sys
from pathlib import Path

# Add parent directory to path for proper imports
sys.path.append(str(Path(__file__).parent.parent))

from streamtrade.data.enhanced_data_manager import EnhancedDataManager
from streamtrade.config.logging_config import setup_logging

def test_debug_logging():
    """Test debug logging untuk loading dan timeframe switching."""
    
    print("🚀 Starting Debug Logging Test")
    print("="*80)
    
    # Setup logging
    setup_logging(level="INFO", console_output=True)
    
    # Initialize Enhanced Data Manager
    data_manager = EnhancedDataManager()
    
    # Test 1: Initial data loading
    print("\n🧪 TEST 1: Initial Data Loading")
    print("-" * 40)
    
    pair = "EURUSD"
    initial_timeframe = "H1"
    days_back = 3
    
    print(f"Loading {pair} {initial_timeframe} for {days_back} days back...")
    
    data = data_manager.load_n_days_back(
        pair=pair,
        timeframe=initial_timeframe,
        days_back=days_back
    )
    
    if data is not None:
        print(f"✅ Initial load successful: {len(data)} candles")
    else:
        print("❌ Initial load failed")
        return
    
    # Test 2: Switch to H4 timeframe
    print("\n🧪 TEST 2: Switch to H4 Timeframe")
    print("-" * 40)
    
    new_timeframe = "H4"
    print(f"Switching {pair} from {initial_timeframe} to {new_timeframe}...")
    
    data_h4 = data_manager.switch_timeframe(pair, new_timeframe)
    
    if data_h4 is not None:
        print(f"✅ H4 switch successful: {len(data_h4)} candles")
    else:
        print("❌ H4 switch failed")
    
    # Test 3: Switch to M15 timeframe
    print("\n🧪 TEST 3: Switch to M15 Timeframe")
    print("-" * 40)
    
    new_timeframe = "M15"
    print(f"Switching {pair} from H4 to {new_timeframe}...")
    
    data_m15 = data_manager.switch_timeframe(pair, new_timeframe)
    
    if data_m15 is not None:
        print(f"✅ M15 switch successful: {len(data_m15)} candles")
    else:
        print("❌ M15 switch failed")
    
    # Test 4: Switch to D1 timeframe (may have limited data)
    print("\n🧪 TEST 4: Switch to D1 Timeframe")
    print("-" * 40)
    
    new_timeframe = "D1"
    print(f"Switching {pair} from M15 to {new_timeframe}...")
    
    data_d1 = data_manager.switch_timeframe(pair, new_timeframe)
    
    if data_d1 is not None:
        print(f"✅ D1 switch successful: {len(data_d1)} candles")
        if len(data_d1) < 3:
            print(f"ℹ️  Note: Limited D1 candles available for {days_back} days")
    else:
        print("❌ D1 switch failed")
    
    # Test 5: Switch back to H1 (should use cache)
    print("\n🧪 TEST 5: Switch Back to H1 (Cache Test)")
    print("-" * 40)
    
    new_timeframe = "H1"
    print(f"Switching {pair} back to {new_timeframe} (should use cache)...")
    
    data_h1_cached = data_manager.switch_timeframe(pair, new_timeframe)
    
    if data_h1_cached is not None:
        print(f"✅ H1 cached switch successful: {len(data_h1_cached)} candles")
    else:
        print("❌ H1 cached switch failed")
    
    # Test 6: Load different pair
    print("\n🧪 TEST 6: Load Different Pair")
    print("-" * 40)
    
    pair2 = "GBPUSD"
    timeframe2 = "H4"
    days_back2 = 2
    
    print(f"Loading {pair2} {timeframe2} for {days_back2} days back...")
    
    data_gbp = data_manager.load_n_days_back(
        pair=pair2,
        timeframe=timeframe2,
        days_back=days_back2
    )
    
    if data_gbp is not None:
        print(f"✅ {pair2} load successful: {len(data_gbp)} candles")
    else:
        print(f"❌ {pair2} load failed")
    
    print("\n🎯 Debug Logging Test Completed!")
    print("="*80)
    
    # Summary
    print("\n📊 SUMMARY:")
    print(f"- Initial {pair} {initial_timeframe}: {'✅' if data is not None else '❌'}")
    print(f"- Switch to H4: {'✅' if data_h4 is not None else '❌'}")
    print(f"- Switch to M15: {'✅' if data_m15 is not None else '❌'}")
    print(f"- Switch to D1: {'✅' if data_d1 is not None else '❌'}")
    print(f"- Switch back to H1 (cached): {'✅' if data_h1_cached is not None else '❌'}")
    print(f"- Load {pair2} {timeframe2}: {'✅' if data_gbp is not None else '❌'}")

if __name__ == "__main__":
    test_debug_logging()
